// Copyright Isto Inc.

using Isto.Core;
using Isto.Core.Data;
using Isto.Core.Inputs;
using Isto.Core.Speedrun;
using Isto.Core.StateMachine;
using Isto.Core.UI;
using Isto.GTW.Configuration;
using Isto.GTW.Providers;
using System.Collections;
using System.Collections.Generic;
using FMODUnity;
using Isto.GTW.Managers;
using Isto.GTW.Player;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using Zenject;

namespace Isto.GTW.UI
{
    /// <summary>
    /// This is the Doinkler Portfolio implementation for Set Game Mode sub state. This allows the player to choose
    /// a level to play in <PERSON><PERSON>ler mode and read their best time for each level.
    /// </summary>
    public class UIDoinklerPortfolio : CoreUISetGameModeSubState
    {
        // UNITY HOOKUP

        [Header("Pop up States")]
        [SerializeField] private UISimpleConfirmModalState _confirmationSubState;
        [SerializeField] private UISimpleTimedModalState _loadingLevelDataSubState;

        [Header("Left Side Of Folder")]
        [SerializeField] private TextMeshProUGUI _fileTitleText;
        [SerializeField] private TextMeshProUGUI _fileSubTitleText;
        [SerializeField] private Transform _levelSelectButtonsContainer;
        [SerializeField] private Transform _folderTabButtonsContainer;
        [SerializeField] private GTWDoinklerPortfolioDefinition _doinklerPortfolioDefinition;
        [SerializeField] private GameObject _levelSelectButtonPrefab;

        [Header("FolderBackground")]
        [SerializeField] private Image _folderBackground;
        [SerializeField] private Color _folderColorBeige = new Color(47, 15, 95);
        [SerializeField] private Color _folderColorBlue = new Color(221, 37, 85);
        [SerializeField] private Color _folderColorRed = new Color(1, 49, 72);

        [Header("Right Side Of Folder")]
        [SerializeField] private Image _selectedLevelImage;
        [SerializeField] private TextMeshProUGUI _selectedLevelNameLabel;
        [SerializeField] private GameObject _levelCompleteImage;
        [SerializeField] private CoreButton _doinklerPlayLevelButton;

        [Header("Bottom of Folder")]
        [SerializeField] private CoreButton _freshStartButton; // aka Reset Progress

        [Header("Speedrun Settings for Doinkler Gameplay")]
        [SerializeField] SpeedrunSettings _doinklerSpeedrunSettings;

        [Header("Audio")]
        [SerializeField] private EventReference _tabSwitchRef;

        [Header("DEV MODE ONLY")]
        [SerializeField] public bool AllLevelsUnlocked = false;


        // OTHER FIELDS

        private GTWGameLevelDefinition _selectedLevel = null;
        private MonoPushdownStateMachine _menuController;
        private List<UIGameLevelDisplay> _gameLevelDisplays; // these are level buttons in the list on the left panel
        private int _currentWorldIndex = 0;
        private Selectable _currentSelection;

        private Color _normalButtonColor;
        private Color _disabledButtonColor = Color.gray;
        private bool _resetButtonAllowed = false;

        // PROPERTIES

        private List<GTWGameWorldDefinition> _gameWorlds => _doinklerPortfolioDefinition.GTWGameWorldDefinitions;


        // INJECTION

        private DiContainer _container;
        private GTWGameState _gameState;
        private IGameData _gameData;
        private IDoinklerWorldDefinitionProvider _doinklerWorldDefinitionProvider;
        private DoinklerLevelVariables _doinklerLevelVariables;
        private LevelVariables _levelVariables;
        private PlayerController _playerController;

        [Inject]
        public void Inject(DiContainer container, GTWGameState gameState, IGameData gameData,
            IDoinklerWorldDefinitionProvider doinklerWorldDefinitionProvider,
            DoinklerLevelVariables doinklerLevelVariables, LevelVariables levelVariables,
            [InjectOptional] PlayerController playerController)
        {
            _container = container;
            _gameState = gameState;
            _gameData = gameData;
            _doinklerWorldDefinitionProvider = doinklerWorldDefinitionProvider;
            _doinklerLevelVariables = doinklerLevelVariables;
            _levelVariables = levelVariables;

            _playerController = playerController;
        }


        // LIFECYCLE METHODS

        private void Awake()
        {
            _normalButtonColor = _freshStartButton.colors.normalColor;
        }

        public override void Enter(MonoStateMachine controller)
        {
            base.Enter(controller);

            SetupStateMachineController(controller);

            SetupFolderTabs();
            SetupScreenVariables(0);
            StartCoroutine(LoadAllDoinklerVariables());
        }

        public override void Exit(MonoStateMachine controller)
        {
            // Restore current save slot Game Data
            int saveSlot = _gameState.SaveSlot;
            _gameData.HasSaveData(saveSlot, (dataExists) =>
            {
                if (dataExists)
                {
                    _gameData.LoadGameData(saveSlot, (success) =>
                    {

                    });
                }
            });

            if (_playerController != null)
            {
                // Respawn the player instead of storing the speedrun timer time.
                _playerController.CheckpointManager.Respawn(showBlackOut: false);
            }

            base.Exit(controller);
        }

        public override MonoState Run(MonoStateMachine controller)
        {
            if (_controls.GetButtonDown(UserActions.UICANCEL))
            {
                if (_doinklerPlayLevelButton.IsSelected && _currentSelection != null)
                {
                    _currentSelection.Select();
                }
                else
                {
                    _menuController.ExitSubState();
                }
            }

            // Handle tab navigation with controller
            if (_controls.GetButtonDown(UserActions.UITABLEFT) || _controls.GetButtonDown(UserActions.UITABRIGHT))
            {
                HandleTabNavigation();
            }

            return this;
        }

        public override void ReturnFromSubState(MonoStateMachine controller, MonoState previousState)
        {
            if (_controls.UsingJoystick())
            {
                if (_freshStartButton != null && _freshStartButton.gameObject.activeInHierarchy && _freshStartButton.interactable)
                {
                    _currentSelection = _freshStartButton;
                    _currentSelection.Select();
                }
                else
                {
                    HighlightFallbackSelectable();
                }
            }
        }


        // EVENT HANDLING

        protected override void RegisterEvents()
        {
            base.RegisterEvents();

            // Subscribe to input mode changes to update navigation when switching between mouse and controller
            Events.Subscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
        }

        protected override void UnregisterEvents()
        {
            base.UnregisterEvents();

            // Unsubscribe from events
            Events.UnSubscribe(Events.INPUT_MODE_CHANGED, Events_OnInputModeChanged);
        }

        private void Events_OnInputModeChanged()
        {
            // If using controller, make sure we have a selection
            if (_controls.UsingJoystick())
            {
                if (_currentSelection != null)
                {
                    _currentSelection.Select();
                }
                else
                {
                    StartCoroutine(SelectFirstLevelAfterTabSwitch());
                }
            }
            else if (!_controls.UsingJoystick() && EventSystem.current.currentSelectedGameObject != null)
            {
                // Clear selection when using mouse
                EventSystem.current.SetSelectedGameObject(null);
            }
        }

        public void FolderTab_OnClick(int fileNumber)
        {
            DisplayTabContents(fileNumber);

            if (_controls.UsingJoystick())
            {
                UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();
                // Make sure we have enough folder tabs and the index is valid
                if (folderTabs.Length > _currentWorldIndex && _currentWorldIndex >= 0)
                {
                    UIDoinklerFolderTab currentTab = folderTabs[_currentWorldIndex];
                    if (currentTab.gameObject.activeInHierarchy)
                    {
                        CoreButton folderButton = currentTab.GetComponentInChildren<CoreButton>();
                        if (folderButton != null && folderButton.interactable)
                        {
                            // Highlight the folder tab
                            _currentSelection = folderButton;
                            _currentSelection.Select();
                        }
                    }
                }
            }

            // Update the controller navigation to include the newly selected folder tab
            StartCoroutine(UpdateFolderTabNavigation());

            if (!_tabSwitchRef.IsNull) RuntimeManager.PlayOneShot(_tabSwitchRef);
        }

        public void LevelDisplayButton_OnPressed(GTWGameLevelDefinition gameLevel)
        {
            DisplayLevelInfo(gameLevel);

            foreach (UIGameLevelDisplay gameLevelDisplay in _gameLevelDisplays)
            {
                // If this is the selected level and we're using a controller, highlight it
                if (gameLevel == gameLevelDisplay.GameLevel)
                {
                    if(gameLevelDisplay.GetComponentInChildren<CoreButton>() is CoreButton levelButton
                    && levelButton != null)
                    {
                        if (levelButton.interactable)
                        {
                            // Track what is level is shown no matter which control mode in case it matters later
                            _currentSelection = levelButton;
                            break;
                        }
                    }
                    else
                    {
                        Debug.LogError($"Non CoreButton UIGameLevelDisplay, must be a setup problem", gameLevelDisplay.gameObject);
                    }
                }
            }

            if (_controls.UsingJoystick())
            {
                // Select the level
                //_currentSelection.Select();

                // or select the play button
                _doinklerPlayLevelButton.Select();
            }
        }

        public void ResetProgress_ButtonClicked()
        {
            // We don't want to prevent interaction with the button because we need to navigate through it,
            // but we need to ignore it while it's looking disabled
            if (!_resetButtonAllowed)
                return;

            // Remember that the fresh start button was highlighted
            if (_controls.UsingJoystick() &&
                _freshStartButton != null &&
                _freshStartButton.gameObject.activeInHierarchy &&
                _freshStartButton.interactable)
            {
                // Make sure the fresh start button is highlighted
                _freshStartButton.Select();
            }

            // Set Cancel to null as we don't want to do anything on cancel
            _confirmationSubState.SetCallbacks(ConfirmationSubState_ResetTimesConfirmed, null);
            _menuController.EnterSubState(_confirmationSubState);
        }

        private void ConfirmationSubState_ResetTimesConfirmed()
        {
            _resetButtonAllowed = false;
            SetupResetButtonColors();

            StartCoroutine(ClearAndSaveLevelData());


            // We used to auto-start the game on reset, I don't like that so now it's disabled.
            // Also if we had to do it, at least respect the async nature of the ClearSaveData calls...
            //StartLevel_ButtonClicked();
        }

        public void StartLevel_ButtonClicked()
        {
            _doinklerWorldDefinitionProvider.Current = _gameWorlds[_currentWorldIndex];
            RebindSpeedrunSettings();
            _gameState.GameLevelDefinition = _selectedLevel;
            int saveSlot = (int)_doinklerWorldDefinitionProvider.Current.SaveSlotId;
            _gameState.LoadSaveSlotMetaData(saveSlot, (gameStateData) =>
            {
                _gameData.HasSaveData(saveSlot, (dataExists) =>
                {
                    if (dataExists)
                    {
                        _gameState.CurrentGameMode = _selectedLevel.gameMode;
                        _gameState.LoadSaveGame(saveSlot);
                    }
                    else
                    {
                        _gameState.SaveSlot = saveSlot;
                        _gameState.StartGameMode(_selectedLevel.gameMode);
                    }
                });
            });
        }


        // OTHER METHODS

        private void DisplayLevelInfo(GTWGameLevelDefinition gameLevel)
        {
            _selectedLevel = gameLevel;
            _selectedLevelImage.sprite = _selectedLevel.LevelImage;
            _selectedLevelNameLabel.text = _selectedLevel.LevelName.ToString();
            _levelCompleteImage.SetActive(_selectedLevel.IsCompleted);

            foreach (UIGameLevelDisplay gameLevelDisplay in _gameLevelDisplays)
            {
                // Update the checkmark
                gameLevelDisplay.NewButtonActive(gameLevel);
            }
        }

        private void DisplayTabContents(int tabNumber)
        {
            // Update the current world index and setup the screen
            SetupScreenVariables(tabNumber);
            SetupLevelsInWorld(tabNumber);

            // Update the folder tabs visuals
            UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();
            foreach (UIDoinklerFolderTab folderTab in folderTabs)
            {
                folderTab.NewTabActive(tabNumber);
            }

            SetFolderColor(tabNumber);
        }

        private void SetupStateMachineController(MonoStateMachine controller)
        {
            if (controller is MonoPushdownStateMachine stateMachine)
            {
                _menuController = stateMachine;
            }
            else
            {
                Debug.LogError("Using level select menu without a push down menu controller, this won't work");
            }
        }

        private void SetupFolderTabs()
        {
            UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();

            for (int i = 0; i < folderTabs.Length; i++)
            {
                folderTabs[i].Setup(this, i);
                folderTabs[i].NewTabActive(0); // The first one
            }
        }

        private void SetFolderColor(int fileNumber)
        {
            if (fileNumber == 0 || fileNumber == 1)
            {
                _folderBackground.color = _folderColorBeige;
            }
            else if (fileNumber == 2 || fileNumber == 3)
            {
                _folderBackground.color = _folderColorBlue;
            }
            else
            {
                _folderBackground.color = _folderColorRed;
            }
        }
        private void SetupScreenVariables(int worldIndex)
        {
            _fileTitleText.text = _gameWorlds[worldIndex].WorldTitle;
            _currentWorldIndex = worldIndex;
        }

        private IEnumerator LoadAllDoinklerVariables()
        {
            var previousMode = _controls.GetControlMode();
            _controls.SetControlMode(Controls.Mode.Disabled);
            int progress = 0;
            int total = _gameWorlds.Count;
            foreach (GTWGameWorldDefinition gtwGameWorldDefinition in _gameWorlds)
            {
                int saveSlot = (int)gtwGameWorldDefinition.SaveSlotId;
                bool requireInit = false;
                _gameData.HasSaveData(saveSlot, (dataExists) =>
                {
                    if (dataExists)
                    {
                        _gameData.LoadGameData(saveSlot, (success) =>
                        {
                            if(success)
                            {
                                foreach (GTWGameLevelDefinition gameLevel in gtwGameWorldDefinition.GameLevels)
                                {
                                    int attempts = _doinklerLevelVariables.GetInt(gameLevel.UniqueIDAttempts);
                                    float bestValue = _doinklerLevelVariables.GetFloat(gameLevel.UniqueIDBest);
                                    float totalValue = _doinklerLevelVariables.GetFloat(gameLevel.UniqueIDTotal);
                                    bool isCompleted = _doinklerLevelVariables.GetBool(gameLevel.UniqueIDCompleted);
                                    gameLevel.SetupSO(attempts, bestValue, totalValue, isCompleted);
                                }
                            }
                            else
                            {
                                foreach (GTWGameLevelDefinition gameLevel in gtwGameWorldDefinition.GameLevels)
                                {
                                    gameLevel.SetupSO(0, 0f, 0f, false);
                                }
                            }

                            progress++;
                        });
                    }
                    else
                    {
                        foreach (GTWGameLevelDefinition gameLevel in gtwGameWorldDefinition.GameLevels)
                        {
                            gameLevel.SetupSO(0, 0f, 0f, false);
                        }
                        progress++;
                    }
                });
            }

            while (progress < total)
            {
                yield return null;
            }

            SetupLevelsInWorld(0);

            _resetButtonAllowed = false; // don't turn it on until we've checked for data
            SetupResetButton();

            yield return SelectFirstLevelAfterTabSwitch();
            _controls.SetControlMode(previousMode);
        }

        private void SetupLevelsInWorld(int worldIndex)
        {
            _gameLevelDisplays = new List<UIGameLevelDisplay>();
            GTWGameWorldDefinition currentGameWorld = _gameWorlds[worldIndex];
            List<GTWGameLevelDefinition> gameLevels = currentGameWorld.GameLevels;
            int saveSlot = (int)currentGameWorld.SaveSlotId;

            bool isNextLevelUnlocked = true;

            foreach (GTWGameLevelDefinition gameLevel in gameLevels)
            {
                gameLevel.IsUnlocked = isNextLevelUnlocked;
                isNextLevelUnlocked = false;

                if (gameLevel.IsCompleted || AllLevelsUnlocked)
                {
                    isNextLevelUnlocked = true;
                }
            }

            // Clear out the current children
            for (int i = _levelSelectButtonsContainer.childCount - 1; i >= 0; i--)
            {
                GameObject.Destroy(_levelSelectButtonsContainer.GetChild(i).gameObject);
            }

            for (int i = 0; i < gameLevels.Count; i++)
            {
                GTWGameLevelDefinition gtwLevel = gameLevels[i];

                // This can be changed to a factory but feels unnecessary for now
                UIGameLevelDisplay gameLevelDisplay = _container.InstantiatePrefabForComponent<UIGameLevelDisplay>(_levelSelectButtonPrefab, _levelSelectButtonsContainer);
                _gameLevelDisplays.Add(gameLevelDisplay);

                bool isUnlocked = false;

                // The first level of every world should be always unlocked
                if (i == 0)
                {
                    isUnlocked = true;
                }
                else if (gtwLevel.IsCompleted)
                {
                    isUnlocked = true;
                }
                // If not completed, check to see if the previous level is completed. If so, unlock it.
                else if (i > 0)
                {
                    if (gameLevels[i - 1].IsCompleted)
                    {
                        isUnlocked = true;
                    }
                }
                gtwLevel.IsUnlocked = isUnlocked;

                // Wait a frame for the component to instantiate
                StartCoroutine(SetUpGameLevelDisplay(gameLevelDisplay, gtwLevel));
            }

            GTWGameLevelDefinition defaultLevel = _gameWorlds[worldIndex].GameLevels[0];
            DisplayLevelInfo(defaultLevel);

            // Note: we could copy the selection logic from LevelDisplayButton_OnPressed if we decide we want to move
            // the selection into the menu here like we used to. For now it should be staying on the reset progress button

            // Setup controller navigation for unlocked levels
            StartCoroutine(SetupControllerNavigation());
        }

        private void SetupResetButton()
        {
            SetupResetButtonColors();

            // Only allowed to delete progress from title
            if (!_gameState.IsInTitleScene())
                return;

            float totalTimeProgress = 0;

            foreach (GTWGameWorldDefinition gtwGameWorldDefinition in _gameWorlds)
            {
                foreach (GTWGameLevelDefinition gtwGameLevelDefinition in gtwGameWorldDefinition.GameLevels)
                {
                    float retrievedTotalValue = gtwGameLevelDefinition.TotalTimeInLevel == "--" ? 0f : float.Parse(gtwGameLevelDefinition.TotalTimeInLevel);
                    totalTimeProgress += retrievedTotalValue;
                }
            }

            // If there is no time progress, it would be sus if we had slots but you never know.
            if (!totalTimeProgress.Approx(0f))
            {
                _resetButtonAllowed = true;
                SetupResetButtonColors();
            }
        }

        private void SetupResetButtonColors()
        {
            ColorBlock colors = _freshStartButton.colors;
            if (_resetButtonAllowed)
            {
                colors.normalColor = _normalButtonColor;
            }
            else
            {
                colors.normalColor = _disabledButtonColor;
            }
            _freshStartButton.colors = colors;
        }

        private void HandleTabNavigation()
        {
            // Determine what type of item is currently highlighted by the controller
            bool isLevelHighlighted = false;
            bool isFreshStartHighlighted = false;
            bool isFolderTabHighlighted = false;

            if (_controls.UsingJoystick() && EventSystem.current.currentSelectedGameObject != null)
            {
                Selectable currentHighlight = EventSystem.current.currentSelectedGameObject.GetComponent<Selectable>();

                if (currentHighlight != null)
                {
                    // Check if the currently highlighted item is the fresh start button
                    if (_freshStartButton != null && _freshStartButton == currentHighlight)
                    {
                        isFreshStartHighlighted = true;
                    }
                    else
                    {
                        // Check if the currently highlighted item is a level button
                        foreach (UIGameLevelDisplay display in _gameLevelDisplays)
                        {
                            CoreButton levelButton = display.GetComponentInChildren<CoreButton>();
                            if (levelButton != null && levelButton == currentHighlight)
                            {
                                isLevelHighlighted = true;
                                break;
                            }
                        }

                        // If it's not a level or fresh start button, check if it's a folder tab
                        if (!isLevelHighlighted && !isFreshStartHighlighted)
                        {
                            UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();
                            foreach (UIDoinklerFolderTab folderTab in folderTabs)
                            {
                                CoreButton folderButton = folderTab.GetComponentInChildren<CoreButton>();
                                if (folderButton != null && folderButton == currentHighlight)
                                {
                                    isFolderTabHighlighted = true;
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            // Calculate the new index based on which button was pressed
            int newIndex;
            if (_controls.GetButtonDown(UserActions.UITABLEFT))
            {
                newIndex = _currentWorldIndex - 1;
                if (newIndex < 0)
                {
                    newIndex = _gameWorlds.Count - 1;
                }
            }
            else // UITABRIGHT
            {
                newIndex = _currentWorldIndex + 1;
                if (newIndex >= _gameWorlds.Count)
                {
                    newIndex = 0;
                }
            }

            // This will update the current world index and setup navigation
            FolderTab_OnClick(newIndex);

            // If a level was highlighted before switching tabs, highlight the first level in the new tab
            if (isLevelHighlighted || _doinklerPlayLevelButton.IsSelected)
            {
                StartCoroutine(SelectFirstLevelAfterTabSwitch());
            }
            // If the fresh start button was highlighted, highlight it again in the new tab
            else if (isFreshStartHighlighted)
            {
                StartCoroutine(SelectFreshStartButtonAfterTabSwitch());
            }
            // If a folder tab was highlighted, the default behavior will highlight the new folder tab
        }

        protected override void HighlightFallbackSelectable()
        {
            // Try to highlight the first level
            foreach (UIGameLevelDisplay display in _gameLevelDisplays)
            {
                CoreButton levelButton = display.GetComponentInChildren<CoreButton>();
                if (levelButton != null && levelButton.interactable)
                {
                    _currentSelection = levelButton;
                    _currentSelection.Select();
                    return;
                }
            }

            // If no level could be highlighted, try to highlight the first level
            // Try to highlight a folder tab first
            UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();
            if (folderTabs.Length > _currentWorldIndex && _currentWorldIndex >= 0)
            {
                CoreButton folderButton = folderTabs[_currentWorldIndex].GetComponentInChildren<CoreButton>();
                if (folderButton != null && folderButton.interactable)
                {
                    _currentSelection = folderButton;
                    _currentSelection.Select();
                    return;
                }
            }

            // If no level could be highlighted, try to highlight the fresh start button
            if (_freshStartButton != null && _freshStartButton.gameObject.activeInHierarchy && _freshStartButton.interactable)
            {
                _currentSelection = _freshStartButton;
                _currentSelection.Select();
            }
        }

        private IEnumerator SetUpGameLevelDisplay(UIGameLevelDisplay gameLevelDisplay, GTWGameLevelDefinition gtwLevel)
        {
            gameLevelDisplay.Setup(gtwLevel, this);
            yield return null;
        }

        private IEnumerator SetupControllerNavigation()
        {
            // Wait a frame to ensure all game level displays are properly set up
            yield return null;

            // Get all folder tabs
            UIDoinklerFolderTab[] folderTabs = _folderTabButtonsContainer.GetComponentsInChildren<UIDoinklerFolderTab>();

            // Get all folder tab buttons
            List<CoreButton> folderButtons = new List<CoreButton>();
            for (int i = 0; i < folderTabs.Length; i++)
            {
                if (folderTabs[i].gameObject.activeInHierarchy)
                {
                    CoreButton folderButton = folderTabs[i].GetComponentInChildren<CoreButton>();
                    if (folderButton != null && folderButton.interactable)
                    {
                        folderButtons.Add(folderButton);
                    }
                }
            }

            // Create a list of level buttons (only unlocked ones)
            List<CoreButton> levelButtons = new List<CoreButton>();
            foreach (UIGameLevelDisplay display in _gameLevelDisplays)
            {
                CoreButton levelButton = display.GetComponentInChildren<CoreButton>();
                if (levelButton != null && levelButton.interactable)
                {
                    levelButtons.Add(levelButton);
                }
            }

            // Get the fresh start button if it's active and interactable
            CoreButton freshStartButton = null;
            if (_freshStartButton != null && _freshStartButton.gameObject.activeInHierarchy && _freshStartButton.interactable)
            {
                freshStartButton = _freshStartButton;
            }

            // Set up horizontal navigation between folder tabs
            if (folderButtons.Count > 0)
            {
                for (int i = 0; i < folderButtons.Count; i++)
                {
                    // Set left navigation
                    if (i > 0)
                    {
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Left, folderButtons[i - 1]);
                    }
                    else if (folderButtons.Count > 1)
                    {
                        // Loop around to the last tab
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Left, folderButtons[folderButtons.Count - 1]);
                    }

                    // Set right navigation
                    if (i < folderButtons.Count - 1)
                    {
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Right, folderButtons[i + 1]);
                    }
                    else if (folderButtons.Count > 1)
                    {
                        // Loop around to the first tab
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Right, folderButtons[0]);
                    }

                    // Set down navigation to the first level button if available
                    if (levelButtons.Count > 0)
                    {
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Down, levelButtons[0]);
                    }
                    else if (freshStartButton != null)
                    {
                        // If no level buttons, set down navigation to the fresh start button
                        UIUtils.SetNavigation(folderButtons[i], UIUtils.NavigationDirection.Down, freshStartButton);
                    }
                }
            }

            // Set up vertical navigation for level buttons
            if (levelButtons.Count > 0)
            {
                // Set up navigation between level buttons
                for (int i = 0; i < levelButtons.Count; i++)
                {
                    // Set up navigation
                    if (i > 0)
                    {
                        UIUtils.SetNavigation(levelButtons[i], UIUtils.NavigationDirection.Up, levelButtons[i - 1]);
                    }
                    else
                    {
                        // Set up navigation to the currently opened folder tab
                        CoreButton activeTab = null;

                        // Find the folder tab that corresponds to the current world index
                        for (int j = 0; j < folderTabs.Length; j++)
                        {
                            if (j == _currentWorldIndex && folderTabs[j].gameObject.activeInHierarchy)
                            {
                                CoreButton folderButton = folderTabs[j].GetComponentInChildren<CoreButton>();
                                if (folderButton != null && folderButton.interactable)
                                {
                                    activeTab = folderButton;
                                    break;
                                }
                            }
                        }

                        if (activeTab != null)
                        {
                            UIUtils.SetNavigation(levelButtons[i], UIUtils.NavigationDirection.Up, activeTab);
                        }
                        else if (folderButtons.Count > 0)
                        {
                            // If we couldn't find the active tab, use the first folder button as a fallback
                            UIUtils.SetNavigation(levelButtons[i], UIUtils.NavigationDirection.Up, folderButtons[0]);
                        }
                    }

                    if (i < levelButtons.Count - 1)
                    {
                        UIUtils.SetNavigation(levelButtons[i], UIUtils.NavigationDirection.Down, levelButtons[i + 1]);
                    }
                    else if (freshStartButton != null)
                    {
                        // Set the last level button to navigate down to the fresh start button
                        UIUtils.SetNavigation(levelButtons[i], UIUtils.NavigationDirection.Down, freshStartButton);

                        // Set the fresh start button to navigate up to the last level button
                        UIUtils.SetNavigation(freshStartButton, UIUtils.NavigationDirection.Up, levelButtons[i]);
                    }
                }
            }
            else if (freshStartButton != null && folderButtons.Count > 0)
            {
                // If there are no level buttons but there is a fresh start button,
                // set up navigation between folder tabs and fresh start button
                foreach (CoreButton folderButton in folderButtons)
                {
                    UIUtils.SetNavigation(folderButton, UIUtils.NavigationDirection.Down, freshStartButton);
                }

                // Find the folder tab that corresponds to the current world index
                CoreButton activeTab = null;
                for (int i = 0; i < folderTabs.Length; i++)
                {
                    if (i == _currentWorldIndex && folderTabs[i].gameObject.activeInHierarchy)
                    {
                        CoreButton folderButton = folderTabs[i].GetComponentInChildren<CoreButton>();
                        if (folderButton != null && folderButton.interactable)
                        {
                            activeTab = folderButton;
                            break;
                        }
                    }
                }

                if (activeTab != null)
                {
                    UIUtils.SetNavigation(freshStartButton, UIUtils.NavigationDirection.Up, activeTab);
                }
                else if (folderButtons.Count > 0)
                {
                    // If we couldn't find the active tab, use the first folder button as a fallback
                    UIUtils.SetNavigation(freshStartButton, UIUtils.NavigationDirection.Up, folderButtons[0]);
                }
            }
        }

        private IEnumerator UpdateFolderTabNavigation()
        {
            // Wait a frame to ensure all UI elements are properly set up
            yield return null;

            // Assuming that we're resetting up the nav to update the link between the open tab button and the first
            // level button. It's overkill if that's the case but I'm keeping it for now.

            // Re-setup the entire navigation to ensure proper order
            // This will set up the navigation from folder tabs to the first level item
            StartCoroutine(SetupControllerNavigation());
        }

        private IEnumerator SelectFirstLevelAfterTabSwitch()
        {
            // Wait a frame to ensure all game level displays are properly set up
            yield return null;

            if (_controls.UsingJoystick())
            {
                // Find the first unlocked level in the new tab
                foreach (UIGameLevelDisplay display in _gameLevelDisplays)
                {
                    CoreButton levelButton = display.GetComponentInChildren<CoreButton>();
                    if (levelButton != null && levelButton.interactable)
                    {
                        // Highlight the first unlocked level
                        _currentSelection = levelButton;
                        _currentSelection.Select();

                        // Make sure navigation is properly set up
                        StartCoroutine(SetupControllerNavigation());
                        break;
                    }
                }
            }
        }

        private IEnumerator SelectFreshStartButtonAfterTabSwitch()
        {
            // Wait a frame to ensure all UI elements are properly set up
            yield return null;

            if (_controls.UsingJoystick())
            {
                // Highlight the fresh start button
                _freshStartButton.Select();

                // Make sure navigation is properly set up
                StartCoroutine(SetupControllerNavigation());
            }
        }

        private void RebindSpeedrunSettings()
        {
            DiContainer projectContainer = ProjectContext.Instance.Container;
            projectContainer.Rebind(typeof(SpeedrunSettings))
                .FromInstance(_doinklerSpeedrunSettings)
                .AsCached()
                .NonLazy();
            projectContainer.Inject(_gameData);
        }

        /// <summary>
        /// Clears and saves level data for all game worlds, resetting progress while preserving best times.
        /// </summary>
        private IEnumerator ClearAndSaveLevelData()
        {
            GTWGameWorldDefinition cachedWorldDefinition = _doinklerWorldDefinitionProvider.Current;
            Controls.Mode previousMode = _controls.GetControlMode();
            _controls.SetControlMode(Controls.Mode.Disabled);
            _menuController.EnterSubState(_loadingLevelDataSubState);

            int progress = 0;
            int total = _gameWorlds.Count;

            foreach (GTWGameWorldDefinition gameWorld in _gameWorlds)
            {
                yield return StartCoroutine(ProcessWorldDataClearing(gameWorld, () => progress++));
            }

            while (progress < total)
            {
                yield return null;
            }

            _doinklerWorldDefinitionProvider.Current = cachedWorldDefinition;
            _controls.SetControlMode(previousMode);

            FinalizeDataClearing();

            _loadingLevelDataSubState.Hide();
        }


        /// <summary>
        /// Processes data clearing for a single game world.
        /// </summary>
        /// <param name="gameWorld">The game world to process.</param>
        /// <param name="currentProgress">The current progress count.</param>
        /// <param name="updateProgress">Callback to update the progress count.</param>
        private IEnumerator ProcessWorldDataClearing(GTWGameWorldDefinition gameWorld, System.Action updateProgress)
        {
            int saveSlot = (int)gameWorld.SaveSlotId;
            _doinklerWorldDefinitionProvider.Current = gameWorld;
            _gameState.SaveSlot = saveSlot;

            bool isProcessingComplete = false;

            _gameData.HasSaveData(saveSlot, (dataExists) =>
            {
                if (dataExists)
                {
                    ProcessExistingSaveData(gameWorld, saveSlot, () =>
                    {
                        isProcessingComplete = true;
                    });
                }
                else
                {
                    Debug.LogError($"Save Slot {saveSlot} doesn't exist: false");
                    isProcessingComplete = true;
                }
            });

            while (!isProcessingComplete)
            {
                yield return null;
            }
        }

        /// <summary>
        /// Handles processing when save data exists for a world.
        /// </summary>
        /// <param name="gameWorld">The game world being processed.</param>
        /// <param name="saveSlot">The save slot number.</param>
        /// <param name="onComplete">Callback when processing is complete.</param>
        private void ProcessExistingSaveData(GTWGameWorldDefinition gameWorld, int saveSlot, System.Action onComplete)
        {
            Debug.LogError($"Save Slot {saveSlot} exists: true");

            _gameData.LoadGameData(saveSlot, (success) =>
            {
                if (success)
                {
                    ClearLevelDataForWorld(gameWorld);
                    ClearGlobalLevelVariables();
                    Debug.LogError($"Save Slot {saveSlot} has been cleared.");

                    _gameData.SaveGameData(saveSlot, false, (_) =>
                    {
                        Debug.LogError($"Updating Save Data for saveslot {saveSlot}");
                        onComplete?.Invoke();
                    });
                }
                else
                {
                    onComplete?.Invoke();
                }
            });
        }

        /// <summary>
        /// Clears level data for all levels in the specified game world.
        /// </summary>
        /// <param name="gameWorld">The game world whose level data should be cleared.</param>
        private void ClearLevelDataForWorld(GTWGameWorldDefinition gameWorld)
        {
            foreach (GTWGameLevelDefinition levelDefinition in gameWorld.GameLevels)
            {
                ResetLevelVariables(levelDefinition);
            }
        }

        /// <summary>
        /// Resets variables for a single level, preserving the best time.
        /// </summary>
        /// <param name="levelDefinition">The level definition to reset.</param>
        private void ResetLevelVariables(GTWGameLevelDefinition levelDefinition)
        {
            int attempts = 0;
            float totalTime = 0f;
            bool isCompleted = false;
            float bestTime = _doinklerLevelVariables.GetFloat(levelDefinition.UniqueIDBest);

            _doinklerLevelVariables.SetInt(levelDefinition.UniqueIDAttempts, attempts);
            _doinklerLevelVariables.SetFloat(levelDefinition.UniqueIDTotal, totalTime);
            _doinklerLevelVariables.SetBool(levelDefinition.UniqueIDCompleted, isCompleted);
            levelDefinition.SetupSO(attempts, bestTime, totalTime, isCompleted);
        }

        /// <summary>
        /// Clears all global level variable dictionaries.
        /// </summary>
        private void ClearGlobalLevelVariables()
        {
            _levelVariables.boolVariables.Clear();
            _levelVariables.intVariables.Clear();
            _levelVariables.floatVariables.Clear();
        }

        /// <summary>
        /// Finalizes the data clearing operation by restoring state and updating the UI.
        /// </summary>
        /// <param name="cachedWorldDefinition">The original world definition to restore.</param>
        /// <param name="previousMode">The previous control mode to restore.</param>
        private void FinalizeDataClearing()
        {
            RebindSpeedrunSettings();

            _currentWorldIndex = 0;
            _selectedLevel = _gameWorlds[0].GameLevels[0];

            DisplayTabContents(_currentWorldIndex);
        }
    }
}